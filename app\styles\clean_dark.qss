/* Clean Dark Theme - Simple and Practical */

/* ===== GLOBAL STYLES ===== */
QWidget {
    font-family: "Segoe UI", "Meiryo UI", "Arial", sans-serif;
    font-size: 9pt;
    color: #ffffff;
    background-color: #323130;
}

/* ===== MAIN WINDOW ===== */
QMainWindow {
    background-color: #201f1e;
    border: none;
}

/* ===== MENU BAR ===== */
QMenuBar {
    background-color: #323130;
    border-bottom: 1px solid #484644;
    padding: 4px 0px;
    color: #ffffff;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 2px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QMenu {
    background-color: #323130;
    border: 1px solid #484644;
    border-radius: 2px;
    padding: 4px;
    color: #ffffff;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 2px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

/* ===== BUTTONS ===== */
QPushButton {
    background-color: #0078d4;
    color: white;
    border: 1px solid #0078d4;
    padding: 6px 12px;
    border-radius: 2px;
    font-weight: normal;
    min-height: 18px;
}

QPushButton:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
    border-color: #005a9e;
}

QPushButton:disabled {
    background-color: #484644;
    color: #8a8886;
    border-color: #605e5c;
}

/* Secondary Button Style */
QPushButton[class="secondary"] {
    background-color: #484644;
    color: #ffffff;
    border: 1px solid #8a8886;
}

QPushButton[class="secondary"]:hover {
    background-color: #605e5c;
    border-color: #a19f9d;
}

/* Danger Button Style */
QPushButton[class="danger"] {
    background-color: #d13438;
    border-color: #d13438;
}

QPushButton[class="danger"]:hover {
    background-color: #a4262c;
    border-color: #a4262c;
}

/* Success Button Style */
QPushButton[class="success"] {
    background-color: #107c10;
    border-color: #107c10;
}

QPushButton[class="success"]:hover {
    background-color: #0e5a0e;
    border-color: #0e5a0e;
}

/* ===== INPUT FIELDS ===== */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #484644;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    color: #ffffff;
    selection-background-color: #0078d4;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #0078d4;
    border-width: 2px;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #323130;
    color: #8a8886;
    border-color: #605e5c;
}

/* ===== COMBO BOX ===== */
QComboBox {
    background-color: #484644;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    min-width: 100px;
    color: #ffffff;
}

QComboBox:hover {
    border-color: #a19f9d;
}

QComboBox:focus {
    border-color: #0078d4;
    border-width: 2px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox QAbstractItemView {
    background-color: #484644;
    border: 1px solid #8a8886;
    border-radius: 2px;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    padding: 2px;
    color: #ffffff;
}

/* ===== SPIN BOX ===== */
QSpinBox, QDoubleSpinBox {
    background-color: #484644;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    min-width: 60px;
    color: #ffffff;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #0078d4;
    border-width: 2px;
}

/* ===== DATE EDIT ===== */
QDateEdit {
    background-color: #484644;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    min-width: 120px;
    color: #ffffff;
}

QDateEdit:focus {
    border-color: #0078d4;
    border-width: 2px;
}

/* ===== CHECK BOX ===== */
QCheckBox {
    spacing: 8px;
    font-size: 9pt;
    color: #ffffff;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #a19f9d;
    border-radius: 2px;
    background-color: #484644;
}

QCheckBox::indicator:hover {
    border-color: #0078d4;
    background-color: #605e5c;
}

QCheckBox::indicator:checked {
    background-color: #0078d4;
    border-color: #0078d4;
}

QCheckBox::indicator:checked:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

QCheckBox::indicator:disabled {
    border-color: #605e5c;
    background-color: #323130;
}

/* ===== GROUP BOX ===== */
QGroupBox {
    font-weight: 600;
    font-size: 9pt;
    color: #ffffff;
    border: 1px solid #8a8886;
    border-radius: 2px;
    margin-top: 8px;
    padding-top: 12px;
    background-color: #323130;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 4px;
    background-color: #323130;
    color: #0078d4;
}

/* ===== LABELS ===== */
QLabel {
    color: #ffffff;
    font-size: 9pt;
}

QLabel[class="heading"] {
    font-size: 12pt;
    font-weight: 600;
    color: #0078d4;
    margin: 4px 0px;
}

QLabel[class="subheading"] {
    font-size: 10pt;
    font-weight: 500;
    color: #ffffff;
    margin: 2px 0px;
}

QLabel[class="caption"] {
    font-size: 8pt;
    color: #a19f9d;
    font-style: italic;
}

/* ===== SPLITTER ===== */
QSplitter::handle {
    background-color: #8a8886;
    border-radius: 1px;
}

QSplitter::handle:horizontal {
    width: 3px;
    margin: 2px 0px;
}

QSplitter::handle:vertical {
    height: 3px;
    margin: 0px 2px;
}

QSplitter::handle:hover {
    background-color: #0078d4;
}

/* ===== TABLE WIDGET ===== */
QTableWidget {
    background-color: #323130;
    border: 1px solid #8a8886;
    border-radius: 2px;
    gridline-color: #484644;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    alternate-background-color: #484644;
    color: #ffffff;
}

QTableWidget::item {
    padding: 6px 8px;
    border: none;
    border-bottom: 1px solid #484644;
}

QTableWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QTableWidget::item:hover {
    background-color: #605e5c;
}

QHeaderView::section {
    background-color: #484644;
    color: #ffffff;
    font-weight: 600;
    padding: 8px;
    border: none;
    border-bottom: 1px solid #8a8886;
    border-right: 1px solid #605e5c;
}

QHeaderView::section:hover {
    background-color: #605e5c;
}

/* ===== LIST WIDGET ===== */
QListWidget {
    background-color: #323130;
    border: 1px solid #8a8886;
    border-radius: 2px;
    selection-background-color: #0078d4;
    selection-color: #ffffff;
    outline: none;
    color: #ffffff;
}

QListWidget::item {
    padding: 8px 12px;
    border: none;
    border-bottom: 1px solid #484644;
    border-radius: 0px;
    margin: 0px;
}

QListWidget::item:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #605e5c;
}

/* ===== SCROLL BAR ===== */
QScrollBar:vertical {
    background-color: #484644;
    width: 12px;
    border-radius: 0px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #8a8886;
    border-radius: 0px;
    min-height: 20px;
    margin: 0px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a19f9d;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #484644;
    height: 12px;
    border-radius: 0px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #8a8886;
    border-radius: 0px;
    min-width: 20px;
    margin: 0px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a19f9d;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* ===== SCROLL AREA ===== */
QScrollArea {
    border: 1px solid #8a8886;
    border-radius: 2px;
    background-color: #323130;
}

/* ===== TAB WIDGET ===== */
QTabWidget::pane {
    border: 1px solid #8a8886;
    border-radius: 2px;
    background-color: #323130;
    margin-top: 2px;
}

QTabBar::tab {
    background-color: #484644;
    color: #ffffff;
    padding: 8px 16px;
    margin-right: 1px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    font-weight: normal;
}

QTabBar::tab:selected {
    background-color: #323130;
    color: #0078d4;
    border-bottom: 2px solid #0078d4;
}

QTabBar::tab:hover:!selected {
    background-color: #605e5c;
}

/* ===== DIALOG STYLES ===== */
QDialog {
    background-color: #323130;
    border-radius: 4px;
    color: #ffffff;
}

/* ===== MODERN NAVIGATION BUTTONS ===== */
QPushButton[class="nav-button"] {
    background-color: transparent;
    color: #ffffff;
    border: none;
    padding: 8px 12px;
    border-radius: 2px;
    text-align: left;
    font-weight: normal;
    margin: 1px 0px;
}

QPushButton[class="nav-button"]:hover {
    background-color: #605e5c;
    color: #0078d4;
}

QPushButton[class="nav-button"]:checked,
QPushButton[class="nav-button"][selected="true"] {
    background-color: #0078d4;
    color: #ffffff;
    font-weight: 500;
}

/* ===== STATUS INDICATORS ===== */
QLabel[class="status-completed"] {
    color: #107c10;
    font-weight: 600;
}

QLabel[class="status-in-progress"] {
    color: #ff8c00;
    font-weight: 600;
}

QLabel[class="status-not-started"] {
    color: #a19f9d;
    font-weight: 600;
}

QLabel[class="status-delayed"] {
    color: #d13438;
    font-weight: 600;
}
