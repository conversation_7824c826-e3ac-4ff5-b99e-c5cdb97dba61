#!/usr/bin/env python3
"""
Demo script to showcase the modern themes for the task management application.
This script creates a simple window with various UI elements to demonstrate
the modern styling without running the full application.
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QLineEdit, QComboBox, QCheckBox, QGroupBox,
    QTableWidget, QTableWidgetItem, QListWidget, QListWidgetItem,
    QTextEdit, QSpinBox, QDateEdit, QTabWidget, QProgressBar
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

# Add the app directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from utils.theme_manager import apply_theme, get_available_themes, set_current_theme
from utils.style_helpers import (
    style_primary_button, style_secondary_button, style_danger_button, 
    style_success_button, style_heading_label, style_subheading_label,
    apply_status_style, make_navigation_button
)


class ModernThemeDemo(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('Modern Theme Demo - Task Management Application')
        self.setGeometry(100, 100, 1000, 700)
        self.setup_ui()
        
    def setup_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        header_label = QLabel('Modern Theme Showcase')
        style_heading_label(header_label)
        main_layout.addWidget(header_label)
        
        # Theme selector
        theme_layout = QHBoxLayout()
        theme_label = QLabel('Select Theme:')
        style_subheading_label(theme_label)
        theme_layout.addWidget(theme_label)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(get_available_themes())
        self.theme_combo.currentTextChanged.connect(self.change_theme)
        theme_layout.addWidget(self.theme_combo)
        theme_layout.addStretch()
        main_layout.addLayout(theme_layout)
        
        # Create tabs to showcase different components
        tab_widget = QTabWidget()
        
        # Buttons tab
        buttons_tab = self.create_buttons_tab()
        tab_widget.addTab(buttons_tab, "Buttons & Controls")
        
        # Forms tab
        forms_tab = self.create_forms_tab()
        tab_widget.addTab(forms_tab, "Forms & Inputs")
        
        # Lists & Tables tab
        lists_tab = self.create_lists_tab()
        tab_widget.addTab(lists_tab, "Lists & Tables")
        
        main_layout.addWidget(tab_widget)
        
    def create_buttons_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Button showcase
        button_group = QGroupBox("Button Styles")
        button_layout = QVBoxLayout(button_group)
        
        # Primary buttons
        primary_layout = QHBoxLayout()
        primary_btn = QPushButton("Primary Button")
        style_primary_button(primary_btn)
        primary_layout.addWidget(primary_btn)
        
        secondary_btn = QPushButton("Secondary Button")
        style_secondary_button(secondary_btn)
        primary_layout.addWidget(secondary_btn)
        
        success_btn = QPushButton("Success Button")
        style_success_button(success_btn)
        primary_layout.addWidget(success_btn)
        
        danger_btn = QPushButton("Danger Button")
        style_danger_button(danger_btn)
        primary_layout.addWidget(danger_btn)
        
        primary_layout.addStretch()
        button_layout.addLayout(primary_layout)
        
        # Navigation buttons
        nav_layout = QHBoxLayout()
        nav_btn1 = QPushButton("Navigation Item 1")
        make_navigation_button(nav_btn1)
        nav_btn1.setChecked(True)
        nav_layout.addWidget(nav_btn1)
        
        nav_btn2 = QPushButton("Navigation Item 2")
        make_navigation_button(nav_btn2)
        nav_layout.addWidget(nav_btn2)
        
        nav_layout.addStretch()
        button_layout.addLayout(nav_layout)
        
        layout.addWidget(button_group)
        
        # Status labels
        status_group = QGroupBox("Status Indicators")
        status_layout = QVBoxLayout(status_group)
        
        status_completed = QLabel("完了 - Completed Task")
        apply_status_style(status_completed, "完了")
        status_layout.addWidget(status_completed)
        
        status_progress = QLabel("進行中 - In Progress Task")
        apply_status_style(status_progress, "進行中")
        status_layout.addWidget(status_progress)
        
        status_not_started = QLabel("未着手 - Not Started Task")
        apply_status_style(status_not_started, "未着手")
        status_layout.addWidget(status_not_started)
        
        status_delayed = QLabel("遅延 - Delayed Task")
        apply_status_style(status_delayed, "遅延")
        status_layout.addWidget(status_delayed)
        
        layout.addWidget(status_group)
        
        # Progress bar
        progress_group = QGroupBox("Progress Indicators")
        progress_layout = QVBoxLayout(progress_group)
        
        progress_bar = QProgressBar()
        progress_bar.setValue(65)
        progress_layout.addWidget(progress_bar)
        
        layout.addWidget(progress_group)
        
        layout.addStretch()
        return widget
        
    def create_forms_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Form inputs
        form_group = QGroupBox("Form Controls")
        form_layout = QVBoxLayout(form_group)
        
        # Text inputs
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("Text Input:"))
        line_edit = QLineEdit()
        line_edit.setPlaceholderText("Enter some text...")
        input_layout.addWidget(line_edit)
        form_layout.addLayout(input_layout)
        
        # Combo box
        combo_layout = QHBoxLayout()
        combo_layout.addWidget(QLabel("Dropdown:"))
        combo_box = QComboBox()
        combo_box.addItems(["Option 1", "Option 2", "Option 3"])
        combo_layout.addWidget(combo_box)
        form_layout.addLayout(combo_layout)
        
        # Spin box
        spin_layout = QHBoxLayout()
        spin_layout.addWidget(QLabel("Number Input:"))
        spin_box = QSpinBox()
        spin_box.setRange(0, 100)
        spin_box.setValue(42)
        spin_layout.addWidget(spin_box)
        form_layout.addLayout(spin_layout)
        
        # Date edit
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Date Input:"))
        date_edit = QDateEdit()
        date_edit.setDate(QDate.currentDate())
        date_edit.setCalendarPopup(True)
        date_layout.addWidget(date_edit)
        form_layout.addLayout(date_layout)
        
        # Checkboxes
        checkbox_layout = QHBoxLayout()
        checkbox1 = QCheckBox("Enable notifications")
        checkbox1.setChecked(True)
        checkbox_layout.addWidget(checkbox1)
        
        checkbox2 = QCheckBox("Auto-save")
        checkbox_layout.addWidget(checkbox2)
        form_layout.addLayout(checkbox_layout)
        
        layout.addWidget(form_group)
        
        # Text area
        text_group = QGroupBox("Text Area")
        text_layout = QVBoxLayout(text_group)
        
        text_edit = QTextEdit()
        text_edit.setPlaceholderText("Enter detailed description here...")
        text_edit.setMaximumHeight(100)
        text_layout.addWidget(text_edit)
        
        layout.addWidget(text_group)
        
        layout.addStretch()
        return widget
        
    def create_lists_tab(self):
        widget = QWidget()
        layout = QHBoxLayout(widget)
        
        # List widget
        list_group = QGroupBox("Project List")
        list_layout = QVBoxLayout(list_group)
        
        list_widget = QListWidget()
        list_widget.addItem("🗃️ All Tasks")
        list_widget.addItem("📂 Project Alpha")
        list_widget.addItem("📂 Project Beta")
        list_widget.addItem("📂 Project Gamma")
        list_widget.setCurrentRow(0)
        list_layout.addWidget(list_widget)
        
        layout.addWidget(list_group)
        
        # Table widget
        table_group = QGroupBox("Task Table")
        table_layout = QVBoxLayout(table_group)
        
        table_widget = QTableWidget(4, 4)
        table_widget.setHorizontalHeaderLabels(["Complete", "Title", "Due Date", "Status"])
        
        # Add sample data
        sample_data = [
            ["✓", "Complete UI Design", "2024-01-15", "完了"],
            ["", "Implement Backend", "2024-01-20", "進行中"],
            ["", "Write Documentation", "2024-01-25", "未着手"],
            ["", "Testing Phase", "2024-01-30", "遅延"]
        ]
        
        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 3:  # Status column
                    apply_status_style(item, value)
                table_widget.setItem(row, col, item)
        
        table_widget.setAlternatingRowColors(True)
        table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        table_layout.addWidget(table_widget)
        
        layout.addWidget(table_group)
        
        return widget
        
    def change_theme(self, theme_name):
        """Change the application theme"""
        set_current_theme(theme_name)
        apply_theme(QApplication.instance())


def main():
    app = QApplication(sys.argv)
    
    # Set default theme
    set_current_theme("Modern Light")
    apply_theme(app)
    
    demo = ModernThemeDemo()
    demo.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
