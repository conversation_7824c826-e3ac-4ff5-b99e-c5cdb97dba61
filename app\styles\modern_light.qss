/* Modern Light Theme for Task Management Application */

/* ===== GLOBAL STYLES ===== */
QWidget {
    font-family: "Segoe UI", "Arial", sans-serif;
    font-size: 9pt;
    color: #2c3e50;
    background-color: #ffffff;
}

/* ===== MAIN WINDOW ===== */
QMainWindow {
    background-color: #f8f9fa;
    border: none;
}

/* ===== MENU BAR ===== */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
    padding: 4px 0px;
    font-weight: 500;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* ===== BUTTONS ===== */
QPushButton {
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 9pt;
    min-height: 16px;
}

QPushButton:hover {
    background-color: #1976d2;
}

QPushButton:pressed {
    background-color: #1565c0;
}

QPushButton:disabled {
    background-color: #bdbdbd;
    color: #757575;
}

/* Secondary Button Style */
QPushButton[class="secondary"] {
    background-color: #f5f5f5;
    color: #424242;
    border: 1px solid #e0e0e0;
}

QPushButton[class="secondary"]:hover {
    background-color: #eeeeee;
    border-color: #bdbdbd;
}

/* Danger Button Style */
QPushButton[class="danger"] {
    background-color: #f44336;
}

QPushButton[class="danger"]:hover {
    background-color: #d32f2f;
}

/* Success Button Style */
QPushButton[class="success"] {
    background-color: #4caf50;
}

QPushButton[class="success"]:hover {
    background-color: #388e3c;
}

/* ===== INPUT FIELDS ===== */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 9pt;
    selection-background-color: #bbdefb;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #2196f3;
    background-color: #fafafa;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #f5f5f5;
    color: #9e9e9e;
    border-color: #e0e0e0;
}

/* ===== COMBO BOX ===== */
QComboBox {
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #bdbdbd;
}

QComboBox:focus {
    border-color: #2196f3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzc1NzU3NSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
    padding: 4px;
}

/* ===== SPIN BOX ===== */
QSpinBox, QDoubleSpinBox {
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 60px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #2196f3;
}

QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #f5f5f5;
    border: none;
    border-radius: 3px;
    width: 16px;
    margin: 2px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #e0e0e0;
}

/* ===== DATE EDIT ===== */
QDateEdit {
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 120px;
}

QDateEdit:focus {
    border-color: #2196f3;
}

QDateEdit::drop-down {
    border: none;
    width: 20px;
}

/* ===== CHECK BOX ===== */
QCheckBox {
    spacing: 8px;
    font-size: 9pt;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #bdbdbd;
    border-radius: 4px;
    background-color: #ffffff;
}

QCheckBox::indicator:hover {
    border-color: #2196f3;
}

QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=);
}

/* ===== GROUP BOX ===== */
QGroupBox {
    font-weight: 600;
    font-size: 10pt;
    color: #424242;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 16px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px;
    background-color: #ffffff;
    color: #1976d2;
}

/* ===== LABELS ===== */
QLabel {
    color: #424242;
    font-size: 9pt;
}

QLabel[class="heading"] {
    font-size: 14pt;
    font-weight: 600;
    color: #1976d2;
    margin: 8px 0px;
}

QLabel[class="subheading"] {
    font-size: 11pt;
    font-weight: 500;
    color: #424242;
    margin: 4px 0px;
}

QLabel[class="caption"] {
    font-size: 8pt;
    color: #757575;
    font-style: italic;
}

/* ===== SPLITTER ===== */
QSplitter::handle {
    background-color: #e0e0e0;
    border-radius: 2px;
}

QSplitter::handle:horizontal {
    width: 4px;
    margin: 4px 0px;
}

QSplitter::handle:vertical {
    height: 4px;
    margin: 0px 4px;
}

QSplitter::handle:hover {
    background-color: #2196f3;
}

/* ===== TABLE WIDGET ===== */
QTableWidget {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    gridline-color: #f0f0f0;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
    alternate-background-color: #fafafa;
}

QTableWidget::item {
    padding: 12px 8px;
    border: none;
    border-bottom: 1px solid #f0f0f0;
}

QTableWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QTableWidget::item:hover {
    background-color: #f5f5f5;
}

QHeaderView::section {
    background-color: #f8f9fa;
    color: #424242;
    font-weight: 600;
    padding: 12px 8px;
    border: none;
    border-bottom: 2px solid #e0e0e0;
    border-right: 1px solid #f0f0f0;
}

QHeaderView::section:hover {
    background-color: #e9ecef;
}

/* ===== LIST WIDGET ===== */
QListWidget {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
    outline: none;
}

QListWidget::item {
    padding: 12px 16px;
    border: none;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 4px;
    margin: 2px;
}

QListWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

QListWidget::item:hover {
    background-color: #f5f5f5;
}

/* ===== SCROLL BAR ===== */
QScrollBar:vertical {
    background-color: #f5f5f5;
    width: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f5f5f5;
    height: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #bdbdbd;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #9e9e9e;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* ===== SCROLL AREA ===== */
QScrollArea {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #ffffff;
}

/* ===== TAB WIDGET ===== */
QTabWidget::pane {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #ffffff;
    margin-top: 4px;
}

QTabBar::tab {
    background-color: #f5f5f5;
    color: #424242;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    color: #1976d2;
    border-bottom: 3px solid #2196f3;
}

QTabBar::tab:hover:!selected {
    background-color: #eeeeee;
}

/* ===== DIALOG STYLES ===== */
QDialog {
    background-color: #ffffff;
    border-radius: 12px;
}

/* ===== MODERN NAVIGATION BUTTONS ===== */
QPushButton[class="nav-button"] {
    background-color: transparent;
    color: #424242;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    text-align: left;
    font-weight: 500;
    margin: 2px 0px;
}

QPushButton[class="nav-button"]:hover {
    background-color: #f5f5f5;
    color: #1976d2;
}

QPushButton[class="nav-button"]:checked,
QPushButton[class="nav-button"][selected="true"] {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 600;
}

/* ===== STATUS INDICATORS ===== */
QLabel[class="status-completed"] {
    color: #4caf50;
    font-weight: 600;
}

QLabel[class="status-in-progress"] {
    color: #ff9800;
    font-weight: 600;
}

QLabel[class="status-not-started"] {
    color: #757575;
    font-weight: 600;
}

QLabel[class="status-delayed"] {
    color: #f44336;
    font-weight: 600;
}

/* ===== MODERN ENHANCEMENTS ===== */

/* Focus indicators */
QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus,
QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

/* Improved button focus */
QPushButton:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.3);
}

/* Card-like containers */
QWidget[class="card"] {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
}

/* Elevated panels */
QGroupBox[class="elevated"] {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Modern tooltips */
QToolTip {
    background-color: #424242;
    color: #ffffff;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 9pt;
    opacity: 0.95;
}

/* Progress indicators */
QProgressBar {
    background-color: #f0f0f0;
    border: none;
    border-radius: 8px;
    height: 8px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #2196f3;
    border-radius: 8px;
}

/* Modern status bar */
QStatusBar {
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    color: #424242;
    font-size: 9pt;
}

/* Improved separator */
QFrame[frameShape="4"] { /* HLine */
    background-color: #e0e0e0;
    max-height: 1px;
    border: none;
}

QFrame[frameShape="5"] { /* VLine */
    background-color: #e0e0e0;
    max-width: 1px;
    border: none;
}

/* Modern slider */
QSlider::groove:horizontal {
    background-color: #e0e0e0;
    height: 4px;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: #2196f3;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #1976d2;
}

/* Notification-style messages */
QLabel[class="notification-success"] {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

QLabel[class="notification-warning"] {
    background-color: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ffcc02;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

QLabel[class="notification-error"] {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ef9a9a;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

/* Modern calendar widget */
QCalendarWidget {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

QCalendarWidget QToolButton {
    background-color: transparent;
    border: none;
    color: #424242;
    font-weight: 500;
    padding: 4px;
}

QCalendarWidget QToolButton:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
}

QCalendarWidget QAbstractItemView {
    background-color: #ffffff;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
}

/* Improved context menus */
QMenu::separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 4px 8px;
}
