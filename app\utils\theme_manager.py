import json
import os
from app.utils.settings_manager import get_setting, set_setting

THEME_SYSTEM = "System"
THEME_QDARKSTYLE = "QDarkStyle"
THEME_QTMATERIAL = "qt-material"
THEME_MODERN_LIGHT = "Modern Light"
THEME_MODERN_DARK = "Modern Dark"
THEME_CLEAN_LIGHT = "Clean Light"
THEME_CLEAN_DARK = "Clean Dark"

def get_current_theme():
    return get_setting("theme", THEME_SYSTEM)

def set_current_theme(theme_name):
    set_setting("theme", theme_name)

def load_qss_file(filename):
    """Load QSS file from styles directory"""
    try:
        # Get the directory where this file is located
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Go up to app directory, then to styles
        styles_dir = os.path.join(os.path.dirname(current_dir), 'styles')
        qss_path = os.path.join(styles_dir, filename)

        if os.path.exists(qss_path):
            with open(qss_path, 'r', encoding='utf-8') as file:
                return file.read()
        else:
            print(f"Warning: QSS file not found: {qss_path}")
            return ""
    except Exception as e:
        print(f"Error loading QSS file {filename}: {e}")
        return ""

def apply_theme(app):
    theme = get_current_theme()
    if theme == THEME_QDARKSTYLE:
        import qdarkstyle
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt5())
    elif theme == THEME_QTMATERIAL:
        from qt_material import apply_stylesheet
        apply_stylesheet(app, theme='dark_teal.xml')
    elif theme == THEME_MODERN_LIGHT:
        qss_content = load_qss_file('modern_light.qss')
        app.setStyleSheet(qss_content)
    elif theme == THEME_MODERN_DARK:
        qss_content = load_qss_file('modern_dark.qss')
        app.setStyleSheet(qss_content)
    elif theme == THEME_CLEAN_LIGHT:
        qss_content = load_qss_file('clean_light.qss')
        app.setStyleSheet(qss_content)
    elif theme == THEME_CLEAN_DARK:
        qss_content = load_qss_file('clean_dark.qss')
        app.setStyleSheet(qss_content)
    else:
        app.setStyleSheet("")

def get_available_themes():
    """Get list of all available themes"""
    return [
        THEME_SYSTEM,
        THEME_CLEAN_LIGHT,
        THEME_CLEAN_DARK,
        THEME_MODERN_LIGHT,
        THEME_MODERN_DARK,
        THEME_QDARKSTYLE,
        THEME_QTMATERIAL
    ]
