"""
Style helper utilities for applying modern styling to PyQt5 widgets.
This module provides functions to easily apply CSS classes and modern styling
to widgets without modifying the core widget logic.
"""

from PyQt5.QtWidgets import QPushButton, QLabel, QWidget
from PyQt5.QtCore import Qt


def apply_button_style(button: QPushButton, style_class: str = "primary"):
    """
    Apply modern button styling to a QPushButton.
    
    Args:
        button: The QPushButton to style
        style_class: The style class to apply ("primary", "secondary", "danger", "success", "nav-button")
    """
    if style_class != "primary":  # primary is default, no class needed
        button.setProperty("class", style_class)
    
    # Force style refresh
    button.style().unpolish(button)
    button.style().polish(button)


def apply_label_style(label: QLabel, style_class: str = "normal"):
    """
    Apply modern label styling to a QLabel.
    
    Args:
        label: The QLabel to style
        style_class: The style class to apply ("heading", "subheading", "caption", "status-completed", 
                    "status-in-progress", "status-not-started", "status-delayed")
    """
    if style_class != "normal":  # normal is default, no class needed
        label.setProperty("class", style_class)
    
    # Force style refresh
    label.style().unpolish(label)
    label.style().polish(label)


def apply_status_style(label: QLabel, status: str):
    """
    Apply status-specific styling to a label based on task status.
    
    Args:
        label: The QLabel to style
        status: The status string ("完了", "進行中", "未着手", "遅延", etc.)
    """
    status_map = {
        "完了": "status-completed",
        "進行中": "status-in-progress", 
        "未着手": "status-not-started",
        "遅延": "status-delayed"
    }
    
    style_class = status_map.get(status, "status-not-started")
    apply_label_style(label, style_class)


def make_navigation_button(button: QPushButton):
    """
    Convert a regular button to a modern navigation button style.
    
    Args:
        button: The QPushButton to convert
    """
    apply_button_style(button, "nav-button")
    button.setCheckable(True)  # Allow toggle state for navigation
    

def set_widget_modern_properties(widget: QWidget, **properties):
    """
    Set multiple modern styling properties on a widget.
    
    Args:
        widget: The widget to style
        **properties: Key-value pairs of properties to set
    """
    for key, value in properties.items():
        widget.setProperty(key, value)
    
    # Force style refresh
    widget.style().unpolish(widget)
    widget.style().polish(widget)


def refresh_widget_style(widget: QWidget):
    """
    Force refresh the styling of a widget.
    Useful when properties have been changed and need to be re-applied.
    
    Args:
        widget: The widget to refresh
    """
    widget.style().unpolish(widget)
    widget.style().polish(widget)
    widget.update()


def apply_modern_form_styling(form_widget: QWidget):
    """
    Apply modern styling to a form container and its children.
    
    Args:
        form_widget: The container widget containing form elements
    """
    # Apply modern spacing and margins
    if hasattr(form_widget, 'layout') and form_widget.layout():
        layout = form_widget.layout()
        layout.setSpacing(12)
        layout.setContentsMargins(16, 16, 16, 16)


def create_modern_button(text: str, style_class: str = "primary", parent=None) -> QPushButton:
    """
    Create a new QPushButton with modern styling applied.
    
    Args:
        text: Button text
        style_class: Style class to apply
        parent: Parent widget
        
    Returns:
        Styled QPushButton
    """
    button = QPushButton(text, parent)
    apply_button_style(button, style_class)
    return button


def create_modern_label(text: str, style_class: str = "normal", parent=None) -> QLabel:
    """
    Create a new QLabel with modern styling applied.
    
    Args:
        text: Label text
        style_class: Style class to apply
        parent: Parent widget
        
    Returns:
        Styled QLabel
    """
    label = QLabel(text, parent)
    apply_label_style(label, style_class)
    return label


def apply_card_styling(widget: QWidget):
    """
    Apply card-like styling to a widget (elevated appearance with shadow effect).
    
    Args:
        widget: The widget to style as a card
    """
    widget.setProperty("class", "card")
    refresh_widget_style(widget)


def apply_modern_table_styling(table_widget):
    """
    Apply modern styling enhancements to a QTableWidget.
    
    Args:
        table_widget: The QTableWidget to style
    """
    # Enable alternating row colors
    table_widget.setAlternatingRowColors(True)
    
    # Set selection behavior
    table_widget.setSelectionBehavior(table_widget.SelectRows)
    
    # Hide grid lines for cleaner look (handled by CSS)
    table_widget.setShowGrid(False)
    
    # Set header properties
    header = table_widget.horizontalHeader()
    if header:
        header.setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        header.setStretchLastSection(True)


def apply_modern_list_styling(list_widget):
    """
    Apply modern styling enhancements to a QListWidget.
    
    Args:
        list_widget: The QListWidget to style
    """
    # Remove focus rectangle
    list_widget.setFocusPolicy(Qt.NoFocus)
    
    # Set selection mode
    list_widget.setSelectionMode(list_widget.SingleSelection)


# Convenience functions for common styling patterns
def style_primary_button(button: QPushButton):
    """Apply primary button styling (blue)"""
    apply_button_style(button, "primary")


def style_secondary_button(button: QPushButton):
    """Apply secondary button styling (gray)"""
    apply_button_style(button, "secondary")


def style_danger_button(button: QPushButton):
    """Apply danger button styling (red)"""
    apply_button_style(button, "danger")


def style_success_button(button: QPushButton):
    """Apply success button styling (green)"""
    apply_button_style(button, "success")


def style_heading_label(label: QLabel):
    """Apply heading label styling (large, bold)"""
    apply_label_style(label, "heading")


def style_subheading_label(label: QLabel):
    """Apply subheading label styling (medium, semi-bold)"""
    apply_label_style(label, "subheading")


def style_caption_label(label: QLabel):
    """Apply caption label styling (small, italic)"""
    apply_label_style(label, "caption")
