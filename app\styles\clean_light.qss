/* Clean Light Theme - Simple and Practical */

/* ===== GLOBAL STYLES ===== */
QWidget {
    font-family: "Segoe UI", "Meiryo UI", "Arial", sans-serif;
    font-size: 9pt;
    color: #323130;
    background-color: #ffffff;
}

/* ===== MAIN WINDOW ===== */
QMainWindow {
    background-color: #faf9f8;
    border: none;
}

/* ===== MENU BAR ===== */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #edebe9;
    padding: 4px 0px;
    color: #323130;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 2px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #deecf9;
    color: #0078d4;
}

QMenu {
    background-color: #ffffff;
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 2px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #deecf9;
    color: #0078d4;
}

/* ===== BUTTONS ===== */
QPushButton {
    background-color: #0078d4;
    color: white;
    border: 1px solid #0078d4;
    padding: 6px 12px;
    border-radius: 2px;
    font-weight: normal;
    min-height: 18px;
}

QPushButton:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
    border-color: #005a9e;
}

QPushButton:disabled {
    background-color: #f3f2f1;
    color: #a19f9d;
    border-color: #edebe9;
}

/* Secondary Button Style */
QPushButton[class="secondary"] {
    background-color: #ffffff;
    color: #323130;
    border: 1px solid #8a8886;
}

QPushButton[class="secondary"]:hover {
    background-color: #f3f2f1;
    border-color: #605e5c;
}

/* Danger Button Style */
QPushButton[class="danger"] {
    background-color: #d13438;
    border-color: #d13438;
}

QPushButton[class="danger"]:hover {
    background-color: #a4262c;
    border-color: #a4262c;
}

/* Success Button Style */
QPushButton[class="success"] {
    background-color: #107c10;
    border-color: #107c10;
}

QPushButton[class="success"]:hover {
    background-color: #0e5a0e;
    border-color: #0e5a0e;
}

/* ===== INPUT FIELDS ===== */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    selection-background-color: #deecf9;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #0078d4;
    border-width: 2px;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #f3f2f1;
    color: #a19f9d;
    border-color: #c8c6c4;
}

/* ===== COMBO BOX ===== */
QComboBox {
    background-color: #ffffff;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #605e5c;
}

QComboBox:focus {
    border-color: #0078d4;
    border-width: 2px;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox QAbstractItemView {
    background-color: #ffffff;
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    selection-background-color: #deecf9;
    selection-color: #323130;
    padding: 2px;
}

/* ===== SPIN BOX ===== */
QSpinBox, QDoubleSpinBox {
    background-color: #ffffff;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    min-width: 60px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #0078d4;
    border-width: 2px;
}

/* ===== DATE EDIT ===== */
QDateEdit {
    background-color: #ffffff;
    border: 1px solid #8a8886;
    border-radius: 2px;
    padding: 6px 8px;
    min-width: 120px;
}

QDateEdit:focus {
    border-color: #0078d4;
    border-width: 2px;
}

/* ===== CHECK BOX ===== */
QCheckBox {
    spacing: 8px;
    font-size: 9pt;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #605e5c;
    border-radius: 2px;
    background-color: #ffffff;
}

QCheckBox::indicator:hover {
    border-color: #0078d4;
    background-color: #f3f2f1;
}

QCheckBox::indicator:checked {
    background-color: #0078d4;
    border-color: #0078d4;
}

QCheckBox::indicator:checked:hover {
    background-color: #106ebe;
    border-color: #106ebe;
}

QCheckBox::indicator:disabled {
    border-color: #c8c6c4;
    background-color: #f3f2f1;
}

/* ===== GROUP BOX ===== */
QGroupBox {
    font-weight: 600;
    font-size: 9pt;
    color: #323130;
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    margin-top: 8px;
    padding-top: 12px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 8px;
    padding: 0 4px;
    background-color: #ffffff;
    color: #0078d4;
}

/* ===== LABELS ===== */
QLabel {
    color: #323130;
    font-size: 9pt;
}

QLabel[class="heading"] {
    font-size: 12pt;
    font-weight: 600;
    color: #0078d4;
    margin: 4px 0px;
}

QLabel[class="subheading"] {
    font-size: 10pt;
    font-weight: 500;
    color: #323130;
    margin: 2px 0px;
}

QLabel[class="caption"] {
    font-size: 8pt;
    color: #605e5c;
    font-style: italic;
}

/* ===== SPLITTER ===== */
QSplitter::handle {
    background-color: #d1d1d1;
    border-radius: 1px;
}

QSplitter::handle:horizontal {
    width: 3px;
    margin: 2px 0px;
}

QSplitter::handle:vertical {
    height: 3px;
    margin: 0px 2px;
}

QSplitter::handle:hover {
    background-color: #0078d4;
}

/* ===== TABLE WIDGET ===== */
QTableWidget {
    background-color: #ffffff;
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    gridline-color: #edebe9;
    selection-background-color: #deecf9;
    selection-color: #323130;
    alternate-background-color: #faf9f8;
}

QTableWidget::item {
    padding: 6px 8px;
    border: none;
    border-bottom: 1px solid #edebe9;
}

QTableWidget::item:selected {
    background-color: #deecf9;
    color: #323130;
}

QTableWidget::item:hover {
    background-color: #f3f2f1;
}

QHeaderView::section {
    background-color: #faf9f8;
    color: #323130;
    font-weight: 600;
    padding: 8px;
    border: none;
    border-bottom: 1px solid #d1d1d1;
    border-right: 1px solid #edebe9;
}

QHeaderView::section:hover {
    background-color: #f3f2f1;
}

/* ===== LIST WIDGET ===== */
QListWidget {
    background-color: #ffffff;
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    selection-background-color: #deecf9;
    selection-color: #323130;
    outline: none;
}

QListWidget::item {
    padding: 8px 12px;
    border: none;
    border-bottom: 1px solid #edebe9;
    border-radius: 0px;
    margin: 0px;
}

QListWidget::item:selected {
    background-color: #deecf9;
    color: #323130;
}

QListWidget::item:hover {
    background-color: #f3f2f1;
}

/* ===== SCROLL BAR ===== */
QScrollBar:vertical {
    background-color: #f3f2f1;
    width: 12px;
    border-radius: 0px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #c8c6c4;
    border-radius: 0px;
    min-height: 20px;
    margin: 0px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a19f9d;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f3f2f1;
    height: 12px;
    border-radius: 0px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #c8c6c4;
    border-radius: 0px;
    min-width: 20px;
    margin: 0px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a19f9d;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* ===== SCROLL AREA ===== */
QScrollArea {
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    background-color: #ffffff;
}

/* ===== TAB WIDGET ===== */
QTabWidget::pane {
    border: 1px solid #d1d1d1;
    border-radius: 2px;
    background-color: #ffffff;
    margin-top: 2px;
}

QTabBar::tab {
    background-color: #f3f2f1;
    color: #323130;
    padding: 8px 16px;
    margin-right: 1px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    font-weight: normal;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    color: #0078d4;
    border-bottom: 2px solid #0078d4;
}

QTabBar::tab:hover:!selected {
    background-color: #edebe9;
}

/* ===== DIALOG STYLES ===== */
QDialog {
    background-color: #ffffff;
    border-radius: 4px;
}

/* ===== MODERN NAVIGATION BUTTONS ===== */
QPushButton[class="nav-button"] {
    background-color: transparent;
    color: #323130;
    border: none;
    padding: 8px 12px;
    border-radius: 2px;
    text-align: left;
    font-weight: normal;
    margin: 1px 0px;
}

QPushButton[class="nav-button"]:hover {
    background-color: #f3f2f1;
    color: #0078d4;
}

QPushButton[class="nav-button"]:checked,
QPushButton[class="nav-button"][selected="true"] {
    background-color: #deecf9;
    color: #0078d4;
    font-weight: 500;
}

/* ===== STATUS INDICATORS ===== */
QLabel[class="status-completed"] {
    color: #107c10;
    font-weight: 600;
}

QLabel[class="status-in-progress"] {
    color: #ff8c00;
    font-weight: 600;
}

QLabel[class="status-not-started"] {
    color: #605e5c;
    font-weight: 600;
}

QLabel[class="status-delayed"] {
    color: #d13438;
    font-weight: 600;
}
