/* Modern Dark Theme for Task Management Application */

/* ===== GLOBAL STYLES ===== */
QWidget {
    font-family: "Segoe UI", "Arial", sans-serif;
    font-size: 9pt;
    color: #e0e0e0;
    background-color: #2b2b2b;
}

/* ===== MAIN WINDOW ===== */
QMainWindow {
    background-color: #1e1e1e;
    border: none;
}

/* ===== MENU BAR ===== */
QMenuBar {
    background-color: #2b2b2b;
    border-bottom: 1px solid #404040;
    padding: 4px 0px;
    font-weight: 500;
    color: #e0e0e0;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 4px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #1565c0;
    color: #ffffff;
}

QMenu {
    background-color: #2b2b2b;
    border: 1px solid #404040;
    border-radius: 8px;
    padding: 4px;
    color: #e0e0e0;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:selected {
    background-color: #1565c0;
    color: #ffffff;
}

/* ===== BUTTONS ===== */
QPushButton {
    background-color: #2196f3;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 9pt;
    min-height: 16px;
}

QPushButton:hover {
    background-color: #1976d2;
}

QPushButton:pressed {
    background-color: #1565c0;
}

QPushButton:disabled {
    background-color: #404040;
    color: #757575;
}

/* Secondary Button Style */
QPushButton[class="secondary"] {
    background-color: #404040;
    color: #e0e0e0;
    border: 1px solid #606060;
}

QPushButton[class="secondary"]:hover {
    background-color: #505050;
    border-color: #808080;
}

/* Danger Button Style */
QPushButton[class="danger"] {
    background-color: #f44336;
}

QPushButton[class="danger"]:hover {
    background-color: #d32f2f;
}

/* Success Button Style */
QPushButton[class="success"] {
    background-color: #4caf50;
}

QPushButton[class="success"]:hover {
    background-color: #388e3c;
}

/* ===== INPUT FIELDS ===== */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #3c3c3c;
    border: 2px solid #606060;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 9pt;
    color: #e0e0e0;
    selection-background-color: #1565c0;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #2196f3;
    background-color: #404040;
}

QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {
    background-color: #2b2b2b;
    color: #757575;
    border-color: #404040;
}

/* ===== COMBO BOX ===== */
QComboBox {
    background-color: #3c3c3c;
    border: 2px solid #606060;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 100px;
    color: #e0e0e0;
}

QComboBox:hover {
    border-color: #808080;
}

QComboBox:focus {
    border-color: #2196f3;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI2UwZTBlMCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #3c3c3c;
    border: 1px solid #606060;
    border-radius: 6px;
    selection-background-color: #1565c0;
    selection-color: #ffffff;
    padding: 4px;
    color: #e0e0e0;
}

/* ===== SPIN BOX ===== */
QSpinBox, QDoubleSpinBox {
    background-color: #3c3c3c;
    border: 2px solid #606060;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 60px;
    color: #e0e0e0;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #2196f3;
}

QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #505050;
    border: none;
    border-radius: 3px;
    width: 16px;
    margin: 2px;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #606060;
}

/* ===== DATE EDIT ===== */
QDateEdit {
    background-color: #3c3c3c;
    border: 2px solid #606060;
    border-radius: 6px;
    padding: 8px 12px;
    min-width: 120px;
    color: #e0e0e0;
}

QDateEdit:focus {
    border-color: #2196f3;
}

QDateEdit::drop-down {
    border: none;
    width: 20px;
}

/* ===== CHECK BOX ===== */
QCheckBox {
    spacing: 8px;
    font-size: 9pt;
    color: #e0e0e0;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 2px solid #b0b0b0;
    border-radius: 3px;
    background-color: #3c3c3c;
}

QCheckBox::indicator:hover {
    border-color: #2196f3;
    background-color: #404040;
}

QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

/* Simple checkmark for dark theme */
QCheckBox::indicator:checked {
    background-color: #2196f3;
    border-color: #2196f3;
}

/* ===== GROUP BOX ===== */
QGroupBox {
    font-weight: 600;
    font-size: 10pt;
    color: #e0e0e0;
    border: 2px solid #606060;
    border-radius: 8px;
    margin-top: 12px;
    padding-top: 16px;
    background-color: #2b2b2b;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px;
    background-color: #2b2b2b;
    color: #2196f3;
}

/* ===== LABELS ===== */
QLabel {
    color: #e0e0e0;
    font-size: 9pt;
}

QLabel[class="heading"] {
    font-size: 14pt;
    font-weight: 600;
    color: #2196f3;
    margin: 8px 0px;
}

QLabel[class="subheading"] {
    font-size: 11pt;
    font-weight: 500;
    color: #e0e0e0;
    margin: 4px 0px;
}

QLabel[class="caption"] {
    font-size: 8pt;
    color: #b0b0b0;
    font-style: italic;
}

/* ===== SPLITTER ===== */
QSplitter::handle {
    background-color: #606060;
    border-radius: 2px;
}

QSplitter::handle:horizontal {
    width: 4px;
    margin: 4px 0px;
}

QSplitter::handle:vertical {
    height: 4px;
    margin: 0px 4px;
}

QSplitter::handle:hover {
    background-color: #2196f3;
}

/* ===== TABLE WIDGET ===== */
QTableWidget {
    background-color: #2b2b2b;
    border: 1px solid #606060;
    border-radius: 8px;
    gridline-color: #404040;
    selection-background-color: #1565c0;
    selection-color: #ffffff;
    alternate-background-color: #323232;
    color: #e0e0e0;
}

QTableWidget::item {
    padding: 12px 8px;
    border: none;
    border-bottom: 1px solid #404040;
}

QTableWidget::item:selected {
    background-color: #1565c0;
    color: #ffffff;
}

QTableWidget::item:hover {
    background-color: #3c3c3c;
}

QHeaderView::section {
    background-color: #323232;
    color: #e0e0e0;
    font-weight: 600;
    padding: 12px 8px;
    border: none;
    border-bottom: 2px solid #606060;
    border-right: 1px solid #404040;
}

QHeaderView::section:hover {
    background-color: #404040;
}

/* ===== LIST WIDGET ===== */
QListWidget {
    background-color: #2b2b2b;
    border: 1px solid #606060;
    border-radius: 8px;
    selection-background-color: #1565c0;
    selection-color: #ffffff;
    outline: none;
    color: #e0e0e0;
}

QListWidget::item {
    padding: 12px 16px;
    border: none;
    border-bottom: 1px solid #404040;
    border-radius: 4px;
    margin: 2px;
}

QListWidget::item:selected {
    background-color: #1565c0;
    color: #ffffff;
}

QListWidget::item:hover {
    background-color: #3c3c3c;
}

/* ===== SCROLL BAR ===== */
QScrollBar:vertical {
    background-color: #323232;
    width: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #606060;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #808080;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #323232;
    height: 12px;
    border-radius: 6px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #606060;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #808080;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* ===== SCROLL AREA ===== */
QScrollArea {
    border: 1px solid #606060;
    border-radius: 8px;
    background-color: #2b2b2b;
}

/* ===== TAB WIDGET ===== */
QTabWidget::pane {
    border: 1px solid #606060;
    border-radius: 8px;
    background-color: #2b2b2b;
    margin-top: 4px;
}

QTabBar::tab {
    background-color: #323232;
    color: #e0e0e0;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: #2b2b2b;
    color: #2196f3;
    border-bottom: 3px solid #2196f3;
}

QTabBar::tab:hover:!selected {
    background-color: #404040;
}

/* ===== DIALOG STYLES ===== */
QDialog {
    background-color: #2b2b2b;
    border-radius: 12px;
    color: #e0e0e0;
}

/* ===== MODERN NAVIGATION BUTTONS ===== */
QPushButton[class="nav-button"] {
    background-color: transparent;
    color: #e0e0e0;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    text-align: left;
    font-weight: 500;
    margin: 2px 0px;
}

QPushButton[class="nav-button"]:hover {
    background-color: #3c3c3c;
    color: #2196f3;
}

QPushButton[class="nav-button"]:checked,
QPushButton[class="nav-button"][selected="true"] {
    background-color: #1565c0;
    color: #ffffff;
    font-weight: 600;
}

/* ===== STATUS INDICATORS ===== */
QLabel[class="status-completed"] {
    color: #4caf50;
    font-weight: 600;
}

QLabel[class="status-in-progress"] {
    color: #ff9800;
    font-weight: 600;
}

QLabel[class="status-not-started"] {
    color: #b0b0b0;
    font-weight: 600;
}

QLabel[class="status-delayed"] {
    color: #f44336;
    font-weight: 600;
}

/* ===== MODERN ENHANCEMENTS ===== */

/* Focus indicators - using border instead of box-shadow */
QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus,
QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
    border-color: #2196f3;
    border-width: 2px;
}

/* Improved button focus */
QPushButton:focus {
    border: 2px solid #2196f3;
}

/* Card-like containers */
QWidget[class="card"] {
    background-color: #2b2b2b;
    border: 1px solid #606060;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
}

/* Elevated panels - using border instead of shadow */
QGroupBox[class="elevated"] {
    background-color: #2b2b2b;
    border: 2px solid #606060;
    border-radius: 8px;
}

/* Modern tooltips */
QToolTip {
    background-color: #1e1e1e;
    color: #e0e0e0;
    border: 1px solid #606060;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 9pt;
    opacity: 0.95;
}

/* Progress indicators */
QProgressBar {
    background-color: #404040;
    border: none;
    border-radius: 8px;
    height: 8px;
    text-align: center;
    color: #e0e0e0;
}

QProgressBar::chunk {
    background-color: #2196f3;
    border-radius: 8px;
}

/* Modern status bar */
QStatusBar {
    background-color: #323232;
    border-top: 1px solid #606060;
    color: #e0e0e0;
    font-size: 9pt;
}

/* Improved separator */
QFrame[frameShape="4"] { /* HLine */
    background-color: #606060;
    max-height: 1px;
    border: none;
}

QFrame[frameShape="5"] { /* VLine */
    background-color: #606060;
    max-width: 1px;
    border: none;
}

/* Modern slider */
QSlider::groove:horizontal {
    background-color: #606060;
    height: 4px;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: #2196f3;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #1976d2;
}

/* Notification-style messages */
QLabel[class="notification-success"] {
    background-color: #1b5e20;
    color: #c8e6c9;
    border: 1px solid #4caf50;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

QLabel[class="notification-warning"] {
    background-color: #e65100;
    color: #ffcc02;
    border: 1px solid #ff9800;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

QLabel[class="notification-error"] {
    background-color: #b71c1c;
    color: #ffcdd2;
    border: 1px solid #f44336;
    border-radius: 4px;
    padding: 8px 12px;
    font-weight: 500;
}

/* Modern calendar widget */
QCalendarWidget {
    background-color: #2b2b2b;
    border: 1px solid #606060;
    border-radius: 8px;
    color: #e0e0e0;
}

QCalendarWidget QToolButton {
    background-color: transparent;
    border: none;
    color: #e0e0e0;
    font-weight: 500;
    padding: 4px;
}

QCalendarWidget QToolButton:hover {
    background-color: #3c3c3c;
    border-radius: 4px;
}

QCalendarWidget QAbstractItemView {
    background-color: #2b2b2b;
    selection-background-color: #1565c0;
    selection-color: #ffffff;
    color: #e0e0e0;
}

/* Improved context menus */
QMenu::separator {
    height: 1px;
    background-color: #606060;
    margin: 4px 8px;
}
