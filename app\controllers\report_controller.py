from PyQt5.QtCore import QObject
from PyQt5.QtWidgets import QApplication
from app.services.report_service import ReportService
from app.views.dialogs.report_dialog import ReportDialog
from app.views.dialogs.template_editor_dialog import TemplateEditorDialog
from config import report_templates
from config.report_templates import load_custom_templates, DEFAULT_TEMPLATE

class ReportController(QObject):
    def __init__(self, selected_tasks, parent_controller=None):
        super().__init__()
        self.parent_controller = parent_controller
        self.selected_tasks = selected_tasks
        self.report_service = ReportService()
        self.current_template_name = 'デフォルト'
        self.current_template = DEFAULT_TEMPLATE

        # Initial report generation
        report_text = self.report_service.generate_report(self.selected_tasks, self.current_template)

        # Setup UI
        self.view = ReportDialog(report_text)
        self.view.set_controller(self)

        self.load_templates()

    def show_dialog(self):
        self.view.exec_()

    def load_templates(self):
        load_custom_templates() # Reload from file
        available_template_names = list(report_templates.AVAILABLE_TEMPLATES.keys())
        self.view.set_template_list(available_template_names)

        # 現在のテンプレートが利用可能かチェック
        if self.current_template_name not in available_template_names:
            # 利用できない場合はデフォルトに切り替え
            self.current_template_name = 'デフォルト'
            self.current_template = report_templates.AVAILABLE_TEMPLATES['デフォルト']
            print(f"現在のテンプレートが利用できません。デフォルトテンプレートに切り替えます。")

        self.view.set_selected_template(self.current_template_name)

    def change_template(self, template_name):
        # 最新版のテンプレートを読み込む
        load_custom_templates()

        if template_name in report_templates.AVAILABLE_TEMPLATES:
            self.current_template_name = template_name
            self.current_template = report_templates.AVAILABLE_TEMPLATES[template_name]
            self.update_report_preview()
            print(f"テンプレートが変更されました: {template_name}")
        else:
            print(f"Warning: Template '{template_name}' not found in available templates: {list(report_templates.AVAILABLE_TEMPLATES.keys())}")
            # テンプレートが削除された場合の対応
            self.current_template_name = 'デフォルト'
            self.current_template = report_templates.AVAILABLE_TEMPLATES['デフォルト']
            self.view.set_selected_template(self.current_template_name)
            self.update_report_preview()

    def update_report_preview(self):
        report_text = self.report_service.generate_report(self.selected_tasks, self.current_template)
        self.view.update_preview(report_text)

    def force_refresh_templates(self):
        """強制的にテンプレートを再読み込みしてUIを更新"""
        print("強制的にテンプレートを再読み込み中...")

        # 現在選択されているテンプレート名を保存
        current_selection = self.current_template_name

        # テンプレートを強制再読み込み
        self.load_templates()

        # 以前選択されていたテンプレートが存在する場合は再選択
        if current_selection in report_templates.AVAILABLE_TEMPLATES:
            self.current_template_name = current_selection
            self.current_template = report_templates.AVAILABLE_TEMPLATES[current_selection]
            self.view.set_selected_template(current_selection)

        # プレビューを更新
        self.update_report_preview()
        print(f"テンプレート再読み込み完了。現在のテンプレート: {self.current_template_name}")

    def copy_to_clipboard(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.view.preview_text.toPlainText())
        self.view.show_message("コピー完了", "日報テキストをクリップボードにコピーしました。")

    def edit_templates(self):
        """テンプレート編集ダイアログを開く"""
        template_editor = TemplateEditorDialog(self.view)
        result = template_editor.exec_()

        if result == template_editor.Accepted:
            # テンプレートが変更された可能性があるので、テンプレートリストを再読み込み
            self.load_templates()

            # 現在選択されているテンプレートが削除されていないかチェック
            if self.current_template_name not in report_templates.AVAILABLE_TEMPLATES:
                # 削除されている場合は、デフォルトテンプレートに切り替え
                self.current_template_name = 'デフォルト'
                self.current_template = report_templates.AVAILABLE_TEMPLATES['デフォルト']
                self.view.set_selected_template(self.current_template_name)
                print(f"削除されたテンプレートを検出しました。デフォルトテンプレートに切り替えます。")

            # 現在のテンプレートで報告を再生成
            self.update_report_preview()
