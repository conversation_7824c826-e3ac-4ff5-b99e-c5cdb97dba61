import requests
from typing import List, Dict
from app.utils.settings_manager import get_setting

class BacklogService:
    """Backlog APIを扱うサービスクラス"""
    
    @staticmethod
    def get_project_list():
        """設定から有効なアカウントのリストを取得"""
        # settings.jsonから設定を読み込む
        credentials = get_setting('backlog_credentials', [])

        # 有効なアカウントのみをフィルタリング
        active_projects = []
        for project in credentials:
            if not project.get('disabled', False):
                active_projects.append(project)

        return active_projects
    
    def __init__(self, project_name=None):
        """
        BacklogServiceの初期化
        project_name: 使用するアカウント名（Noneの場合は最初の有効なアカウントを使用）
        """
        active_projects = self.get_project_list()
        
        if not active_projects:
            raise ValueError("有効なBacklogアカウントが設定されていません")
        
        # 指定されたアカウント名が存在する場合はそれを使用し、
        # 指定がなければ最初の有効なアカウントを使用
        self.project = None
        
        if project_name:
            for acc in active_projects:
                if acc['name'] == project_name:
                    self.project = acc
                    break
            
            if not self.project:
                raise ValueError(f"アカウント '{project_name}' が見つかりませんでした")
        else:
            # 最初の有効なアカウントを使用
            self.project = active_projects[0]
        
        self.base_url = self.project['base_url']
        self.api_key = self.project['api_key']
        
        # 接続を検証
        self.validate_connection()

    @staticmethod
    def get_all_instances():
        """全ての設定されたバックログインスタンスを取得"""
        instances = []

        try:
            # settings.jsonから設定を読み込む
            credentials = get_setting('backlog_credentials', [])

            for cred in credentials:
                if not cred.get('disabled', False) and 'base_url' in cred and 'api_key' in cred:
                    try:
                        instance = BacklogService(cred['name'])
                        instances.append(instance)
                    except Exception:
                        # 個別のインスタンス初期化失敗は無視
                        pass

        except Exception:
            pass

        return instances

    def validate_connection(self):
        """接続と認証が正しいか検証する"""
        try:
            self.get_myself()
        except requests.exceptions.RequestException as e:
            raise ConnectionError(f"Backlogに接続できません: {str(e)}\nURLとAPI Keyが正しいか確認してください。")

    def get_my_tasks(self) -> List[Dict]:
        """自分に割り当てられたタスクを取得"""
        # 自分の情報を取得
        myself = self.get_myself()
        if not myself:
            raise Exception("ユーザー情報を取得できません")

        # APIのURLを構築
        url = f"{self.base_url}/api/v2/issues"
        
        # サーバー側でフィルタリングするために、assigneeIdパラメータを設定
        params = {
            'apiKey': self.api_key,
            'assigneeId[]': myself['id'],  # 自分に割り当てられたタスクのみを取得
            'sort': 'updated',
            'order': 'desc',
            'count': 100  # 1ページあたりの最大数
        }

        all_tasks = []
        page = 1
        
        while True:
            # ページングパラメータを設定
            params['offset'] = (page - 1) * 100
            
            # リクエストを送信
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            page_tasks = response.json()
            if not page_tasks:  # 結果が空なら終了
                break
                
            all_tasks.extend(page_tasks)
            
            # 結果が100件未満なら、これが最後のページ
            if len(page_tasks) < 100:
                break
                
            page += 1
        
        # タスクの重要な情報のみ返す
        processed_tasks = []
        for task in all_tasks:
            project_info = task.get('project', {}) # プロジェクト情報を取得、なければ空辞書
            processed_tasks.append({
                'summary': task['summary'],
                'issueKey': task['issueKey'],
                'status': task['status']['name'],
                'description': task.get('description', ''),
                'startDate': task.get('startDate', ''),
                'dueDate': task.get('dueDate', ''),
                'projectId': project_info.get('id'), # プロジェクトIDを取得
                'projectName': project_info.get('name'), # プロジェクト名を取得
                'estimatedHours': task.get('estimatedHours'), # 予定時間を追加
                'actualHours': task.get('actualHours'),       # 実績時間を追加
                'source_url': self.base_url  # タスクのソースURLを追加
            })
        
        return processed_tasks

    def get_myself(self) -> Dict:
        """現在のユーザー情報を取得"""
        url = f"{self.base_url}/api/v2/users/myself"
        params = {'apiKey': self.api_key}
        
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def get_tasks(self) -> List[Dict]:
        """自分に割り当てられたタスクをTask用に整形して取得"""
        backlog_tasks = self.get_my_tasks()
        
        processed_tasks = []
        for task in backlog_tasks:
            # Backlogのステータスをローカルステータスに変換
            status_mapping = {
                '未対応': '未着手',
                '処理中': '進行中',
                '処理済み': '完了',
                '完了': '完了'
            }
            
            status = status_mapping.get(task['status'], '未着手')
            
            # 日付フォーマットを変換 (YYYY-MM-DD to YYYY/MM/DD)
            due_date = ""
            if task.get('dueDate'):
                try:
                    date_parts = task['dueDate'].split('-')
                    due_date = f"{date_parts[0]}/{date_parts[1]}/{date_parts[2]}"
                except (ValueError, IndexError):
                    due_date = task['dueDate']  # 変換できない場合はそのまま
            
            # プロジェクト名を取得
            project_name = f"Backlog: {self.project['name']}"
            
            # タスクへのURLを構築
            issue_key = task.get('issueKey', '')
            source_url = f"{self.base_url}/view/{issue_key}" if issue_key else ""
            
            processed_tasks.append({
                'title': task['summary'],
                'description': task.get('description', ''),
                'due_date': due_date,
                'status': status,
                'project': project_name,
                'issue_key': issue_key,
                'backlog_project_id': self.project.get('id', ''),
                'source_url': source_url
            })
            
        return processed_tasks
