{"テスト用テンプレート": {"project_title": "", "task_title": "  ▶ {index}. {issue_key} {title}\n", "task_details": "    進捗状況：{progress}\n    予定期間：{start_date} ～ {due_date}\n    実績期間：{actual_period}\n    実績時間：{actual_time:.1f} h\n    コメント：{comment}\n", "comment_history_header": "    ▷ 進捗記録：\n", "comment_entry": "      - {timestamp} {text}\n", "task_separator": "\n", "project_separator": "\n\n"}, "デモ用テンプレート": {"project_title": "🚀 プロジェクト: {project_name}\n", "task_title": "  📋 {index}. [{issue_key}] {title}\n", "task_details": "      ⏰ 期間: {start_date} → {due_date}\n      📊 進捗: {progress}\n      ⏱️  実績: {actual_time:.1f}h / {estimated_time:.1f}h\n      💬 備考: {comment}\n", "comment_history_header": "      📝 履歴:\n", "comment_entry": "        • {timestamp}: {text}\n", "task_separator": "\n", "project_separator": "\n==================================================\n\n"}, "123123": {"project_title": "● {project_name}\n", "task_title": "    {index}. {issue_key} {title}{issue_key}\n", "task_details": "　　　　進捗状況：{progress}\n　　　　予定期間：{start_date} ～ {due_date}\n　　　　実績期間：{actual_period}\n　　　　実績時間：{actual_time:.1f} h\n　　　　遅れ要因：特になし\n　　　　コメント：{comment}\n", "comment_history_header": "　　　　進捗記録：\n", "comment_entry": "　　　　　　　{timestamp} {text}\n", "task_separator": "\n", "project_separator": "\n"}}