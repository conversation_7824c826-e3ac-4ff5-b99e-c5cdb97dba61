# タスク管理ツール

## プロジェクト概要

このタスク管理ツールは、個人およびチームのタスク管理を効率化するためのデスクトップアプリケーションです。PyQt5を使用したGUIアプリケーションで、タスクの作成・編集・削除、リマインダー機能、日報生成、Backlog連携などの機能を提供します。

### 主な特徴
- 📋 **包括的なタスク管理**: タスクの作成、編集、削除、ステータス管理
- ⏰ **スマートリマインダー**: Windows Toast通知によるタスクリマインダー
- 📊 **日報生成**: カスタマイズ可能なテンプレートによる日報自動生成
- 🔗 **Backlog連携**: Backlogプロジェクトとの双方向同期
- 💬 **コメント機能**: タスクに対する進捗記録とコメント管理
- 🎨 **テーマ対応**: 複数のUIテーマ（System、QDarkStyle、qt-material）
- 🏗️ **MVC アーキテクチャ**: 保守性と拡張性を重視した設計

## アーキテクチャ

### 技術スタック
- **GUI フレームワーク**: PyQt5
- **データベース**: SQLite (SQLAlchemy ORM)
- **通知システム**: winotify (Windows Toast通知)
- **HTTP クライアント**: requests (Backlog API連携)
- **テーマ**: qdarkstyle, qt-material

### アーキテクチャパターン
本プロジェクトは **MVC (Model-View-Controller)** パターンを採用しています：

```
app/
├── models/          # データモデル層
│   ├── task.py      # タスクモデル
│   ├── comment.py   # コメントモデル
│   └── base.py      # SQLAlchemy基底クラス
├── views/           # ビュー層
│   ├── main_window.py    # メインウィンドウ
│   └── dialogs/          # ダイアログ群
├── controllers/     # コントローラー層
│   ├── main_controller.py      # メインコントローラー
│   ├── task_controller.py      # タスク管理
│   ├── reminder_controller.py  # リマインダー管理
│   ├── report_controller.py    # 日報生成
│   └── backlog_controller.py   # Backlog連携
├── services/        # ビジネスロジック層
│   ├── task_service.py         # タスクサービス
│   ├── reminder_service.py     # リマインダーサービス
│   └── backlog_service.py      # Backlog APIサービス
└── utils/           # ユーティリティ
    ├── theme_manager.py        # テーマ管理
    └── settings_manager.py     # 設定管理
```

## セットアップ手順

### 前提条件
- Python 3.8以上
- Windows OS (Toast通知機能のため)

### インストール手順

1. **リポジトリのクローン**
   ```bash
   git clone <repository-url>
   cd taskmanage
   ```

2. **仮想環境の作成と有効化**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   ```

3. **依存関係のインストール**
   ```bash
   pip install -r requirements.txt
   ```

4. **データベースの初期化**
   ```bash
   python main.py
   ```
   初回起動時に自動的にSQLiteデータベース（`app.db`）が作成されます。

5. **設定ファイルの準備**（オプション）
   - Backlog連携を使用する場合は `config/settings.py` を編集
   - カスタムテンプレートは `config/custom_templates.json` で管理

### 起動方法
```bash
python main.py
```

## 主要機能

### 1. タスク管理
- ✅ タスクの作成、編集、削除
- ✅ ステータス管理（未着手、進行中、完了など）
- ✅ 期限日、開始日の設定
- ✅ 予定時間・実績時間の記録
- ✅ プロジェクト別分類
- ✅ 繰り返しタスクの設定

### 2. リマインダー機能
- ✅ Windows Toast通知
- ✅ タスク別リマインダー設定
- ✅ 期限日前の自動通知
- ✅ 起動時の過去リマインドチェック
- ✅ カスタマイズ可能な通知タイミング

### 3. 日報生成
- ✅ カスタマイズ可能なテンプレート
- ✅ JSON形式でのテンプレート編集
- ✅ リアルタイムプレビュー
- ✅ 変数挿入支援
- ✅ フォーマット保持機能

### 4. Backlog連携
- ✅ Backlog APIとの双方向同期
- ✅ 複数アカウント対応
- ✅ 自動タスク同期
- ✅ Issue Key管理

### 5. コメント機能
- ✅ タスクへのコメント追加
- ✅ タイムスタンプ付き進捗記録
- ✅ コメント履歴表示

### 6. テーマ機能
- ✅ システムテーマ
- ✅ ダークテーマ（QDarkStyle）
- ✅ マテリアルテーマ（qt-material）

## コード構造

### ディレクトリ構成
```
taskmanage/
├── main.py                    # アプリケーションエントリーポイント
├── requirements.txt           # Python依存関係
├── app.db                    # SQLiteデータベース
├── logs/                     # ログファイル
├── config/                   # 設定ファイル
│   ├── settings.py           # アプリケーション設定
│   ├── settings.json         # JSON設定
│   ├── report_templates.py   # 日報テンプレート
│   └── custom_templates.json # カスタムテンプレート
└── app/                      # メインアプリケーション
    ├── models/               # データモデル
    ├── views/                # ユーザーインターフェース
    ├── controllers/          # ビジネスロジック制御
    ├── services/             # サービス層
    ├── database/             # データベース関連
    └── utils/                # ユーティリティ
```

### 主要ファイルの説明

#### エントリーポイント
- `main.py`: アプリケーションの起動点、データベース初期化とMVCコンポーネントの組み立て

#### モデル層 (`app/models/`)
- `task.py`: タスクデータモデル（SQLAlchemy ORM）
- `comment.py`: コメントデータモデル
- `base.py`: SQLAlchemy基底クラス
- `custom_types.py`: カスタムデータ型定義

#### ビュー層 (`app/views/`)
- `main_window.py`: メインウィンドウUI
- `dialogs/`: 各種ダイアログウィンドウ

#### コントローラー層 (`app/controllers/`)
- `main_controller.py`: メインコントローラー、アプリケーション全体の制御
- `task_controller.py`: タスク操作の制御
- `reminder_controller.py`: リマインダー機能の制御
- `report_controller.py`: 日報生成の制御
- `backlog_controller.py`: Backlog連携の制御
- `filter_controller.py`: タスクフィルタリングの制御
- `format_controller.py`: データフォーマットの制御
- `comment_controller.py`: コメント機能の制御

#### サービス層 (`app/services/`)
- `task_service.py`: タスクビジネスロジック
- `reminder_service.py`: リマインダーサービス、Windows通知
- `backlog_service.py`: Backlog API連携
- `report_service.py`: 日報生成サービス

#### データベース層 (`app/database/`)
- `db.py`: データベース接続とセッション管理
- `reminder_manager.py`: リマインダー設定管理

#### ユーティリティ (`app/utils/`)
- `theme_manager.py`: UIテーマ管理
- `settings_manager.py`: アプリケーション設定管理

## 開発ガイドライン

### コーディング規約

#### Python コーディングスタイル
- **PEP 8** に準拠したコーディングスタイルを採用
- インデント: スペース4文字
- 行の最大長: 120文字
- 関数・変数名: snake_case
- クラス名: PascalCase
- 定数: UPPER_CASE

#### ファイル命名規則
- Python ファイル: `snake_case.py`
- クラスファイル: クラス名に対応した `snake_case.py`
- 設定ファイル: `settings.py`, `config.json`

#### コメント・ドキュメント
```python
def create_task(title: str, description: Optional[str] = None) -> Task:
    """
    新しいタスクを作成する

    Args:
        title (str): タスクのタイトル
        description (Optional[str]): タスクの説明

    Returns:
        Task: 作成されたタスクオブジェクト

    Raises:
        ValueError: タイトルが空の場合
    """
    pass
```

### MVC パターンの実装指針

#### Model (モデル)
- データベーステーブルとの1対1対応
- ビジネスロジックは含めない
- SQLAlchemy ORMを使用
- バリデーションはプロパティで実装

#### View (ビュー)
- PyQt5 ウィジェットの組み立てのみ
- ビジネスロジックは含めない
- コントローラーとのシグナル・スロット接続
- UI状態の管理のみ

#### Controller (コントローラー)
- ビジネスロジックの制御
- モデルとビューの仲介
- サービス層の呼び出し
- エラーハンドリング

### 新機能追加の手順

1. **要件定義**
   - 機能仕様の明確化
   - UI/UXの設計
   - データベース設計の検討

2. **モデルの実装**
   - 必要に応じてデータベーステーブルの追加
   - SQLAlchemy モデルの定義
   - マイグレーション（必要な場合）

3. **サービス層の実装**
   - ビジネスロジックの実装
   - データアクセス層の実装
   - 単体テストの作成

4. **コントローラーの実装**
   - UI制御ロジックの実装
   - サービス層との連携
   - エラーハンドリング

5. **ビューの実装**
   - PyQt5 UIの実装
   - シグナル・スロットの接続
   - スタイリング

6. **統合テスト**
   - 機能テストの実行
   - UIテストの実行
   - パフォーマンステスト

### Git ワークフロー

#### ブランチ戦略
- `main`: 本番環境用の安定版
- `develop`: 開発用の統合ブランチ
- `feature/*`: 機能開発用ブランチ
- `hotfix/*`: 緊急修正用ブランチ

#### コミットメッセージ
```
feat: 新機能の追加
fix: バグ修正
docs: ドキュメントの更新
style: コードフォーマットの修正
refactor: リファクタリング
test: テストの追加・修正
chore: その他の変更
```

## API/インターフェース仕様

### 主要クラス

#### TaskService クラス
タスクの CRUD 操作を提供するサービスクラス

```python
class TaskService:
    @staticmethod
    def create_task(title: str, description: str = None, **kwargs) -> Task:
        """新しいタスクを作成"""

    @staticmethod
    def get_task_by_id(task_id: int) -> Optional[Task]:
        """IDでタスクを取得"""

    @staticmethod
    def update_task(task_id: int, **kwargs) -> bool:
        """タスクを更新"""

    @staticmethod
    def delete_task(task_id: int) -> bool:
        """タスクを削除"""

    @staticmethod
    def get_all_tasks() -> List[Task]:
        """すべてのタスクを取得"""
```

#### ReminderService クラス
リマインダー機能を提供するサービスクラス

```python
class ReminderService:
    def __init__(self, tasks: List[Task]):
        """リマインダーサービスを初期化"""

    def start(self):
        """リマインダーサービスを開始"""

    def stop(self):
        """リマインダーサービスを停止"""

    def send_notification(self, task: Task):
        """Windows Toast通知を送信"""
```

#### BacklogService クラス
Backlog API との連携を提供するサービスクラス

```python
class BacklogService:
    def __init__(self, space_name: str, api_key: str):
        """Backlog APIクライアントを初期化"""

    def get_my_tasks(self) -> List[Dict]:
        """自分に割り当てられたタスクを取得"""

    def get_project_list(self) -> List[Dict]:
        """プロジェクト一覧を取得"""
```

### データベーススキーマ

#### tasks テーブル
```sql
CREATE TABLE tasks (
    id INTEGER PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    due_date DATETIME,
    status VARCHAR(50) DEFAULT '未着手',
    project VARCHAR(100),
    completed BOOLEAN DEFAULT FALSE,
    repeat VARCHAR(50),
    start_date DATETIME,
    estimated_time FLOAT,
    actual_time FLOAT DEFAULT 0.0,
    actual_start_date DATETIME,
    actual_end_date DATETIME,
    source VARCHAR(50),
    issue_key VARCHAR(50),
    backlog_project_id VARCHAR(50),
    source_url VARCHAR(512),
    reminder_enabled BOOLEAN DEFAULT FALSE,
    reminder_days INTEGER
);
```

#### comments テーブル
```sql
CREATE TABLE comments (
    id INTEGER PRIMARY KEY,
    text TEXT NOT NULL,
    task_id INTEGER NOT NULL,
    timestamp VARCHAR(50) NOT NULL,
    FOREIGN KEY (task_id) REFERENCES tasks (id)
);
```

### 設定ファイル仕様

#### config/settings.py
```python
# データベース設定
DATABASE_URI = 'sqlite:///app.db'

# アプリケーション設定
APP_NAME = "Task Manager"

# Backlog API設定（オプション）
BACKLOG_CREDENTIALS = [
    {
        'space_name': 'your-space',
        'api_key': 'your-api-key',
        'disabled': False
    }
]
```

#### config/custom_templates.json
```json
{
    "カスタムテンプレート名": {
        "project_title": "● {project_name}\n",
        "task_title": "    {index}. {issue_key} {title}\n",
        "task_details": "詳細情報のフォーマット",
        "comment_history_header": "進捗記録ヘッダー",
        "comment_entry": "コメントエントリーフォーマット",
        "task_separator": "\n",
        "project_separator": "\n"
    }
}
```

## テスト

### テスト戦略
本プロジェクトでは以下のテスト戦略を採用しています：

- **単体テスト**: 各サービスクラスとユーティリティ関数
- **統合テスト**: コントローラーとサービス間の連携
- **UIテスト**: 主要な画面操作とワークフロー
- **システムテスト**: エンドツーエンドの機能テスト

### テスト実行方法

#### 前提条件
```bash
pip install pytest pytest-qt pytest-mock
```

#### テスト実行
```bash
# すべてのテストを実行
pytest

# 特定のテストファイルを実行
pytest tests/test_task_service.py

# カバレッジレポート付きで実行
pytest --cov=app tests/
```

### テストファイル構成
```
tests/
├── __init__.py
├── conftest.py                 # pytest設定とフィクスチャ
├── test_models/               # モデルテスト
│   ├── test_task.py
│   └── test_comment.py
├── test_services/             # サービステスト
│   ├── test_task_service.py
│   ├── test_reminder_service.py
│   └── test_backlog_service.py
├── test_controllers/          # コントローラーテスト
│   ├── test_main_controller.py
│   └── test_task_controller.py
└── test_utils/                # ユーティリティテスト
    ├── test_theme_manager.py
    └── test_settings_manager.py
```

### テスト例

#### サービステストの例
```python
import pytest
from app.services.task_service import TaskService
from app.models.task import Task

class TestTaskService:
    def test_create_task(self):
        """タスク作成のテスト"""
        task = TaskService.create_task(
            title="テストタスク",
            description="テスト用のタスクです"
        )
        assert task.title == "テストタスク"
        assert task.description == "テスト用のタスクです"
        assert task.status == "未着手"

    def test_get_task_by_id(self):
        """タスク取得のテスト"""
        # テストデータの準備
        task = TaskService.create_task(title="取得テスト")

        # テスト実行
        retrieved_task = TaskService.get_task_by_id(task.id)

        # 検証
        assert retrieved_task is not None
        assert retrieved_task.title == "取得テスト"
```

#### UIテストの例
```python
import pytest
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest
from app.views.main_window import MainWindow
from app.controllers.main_controller import MainController

class TestMainWindow:
    def test_task_creation_dialog(self, qtbot):
        """タスク作成ダイアログのテスト"""
        controller = MainController()
        window = MainWindow(controller)
        qtbot.addWidget(window)

        # タスク追加ボタンをクリック
        qtbot.mouseClick(window.add_task_button, Qt.LeftButton)

        # ダイアログが表示されることを確認
        assert window.task_dialog.isVisible()
```

## トラブルシューティング

### よくある問題と解決方法

#### 1. アプリケーションが起動しない

**症状**: `python main.py` を実行してもアプリケーションが起動しない

**原因と解決方法**:
- **依存関係の不足**: `pip install -r requirements.txt` を実行
- **Python バージョン**: Python 3.8以上を使用しているか確認
- **PyQt5 の問題**: `pip install --upgrade PyQt5` でアップデート

#### 2. データベースエラー

**症状**: `sqlite3.OperationalError` や `sqlalchemy.exc.OperationalError`

**解決方法**:
```bash
# データベースファイルを削除して再作成
rm app.db
python main.py
```

#### 3. Windows Toast通知が表示されない

**症状**: リマインダーが設定されているのに通知が表示されない

**原因と解決方法**:
- **Windows通知設定**: Windows設定 > システム > 通知とアクションで通知が有効になっているか確認
- **winotify の問題**: `pip install --upgrade winotify` でアップデート
- **ログの確認**: `logs/task_manager.log` でエラーメッセージを確認

#### 4. Backlog連携エラー

**症状**: Backlogからタスクを取得できない

**解決方法**:
- **API キーの確認**: `config/settings.py` のAPI キーが正しいか確認
- **ネットワーク接続**: インターネット接続を確認
- **Backlog サービス状況**: Backlogのサービス状況を確認

#### 5. テーマが適用されない

**症状**: テーマを変更してもUIが変わらない

**解決方法**:
- **アプリケーション再起動**: テーマ変更後にアプリケーションを再起動
- **テーマライブラリの確認**: `pip install --upgrade qdarkstyle qt-material`

#### 6. 日報生成エラー

**症状**: 日報生成時にエラーが発生する

**解決方法**:
- **テンプレートファイルの確認**: `config/custom_templates.json` の JSON 形式が正しいか確認
- **変数名の確認**: テンプレート内の変数名が正しいか確認
- **ファイル権限**: `config/` ディレクトリの書き込み権限を確認

### ログファイルの確認

アプリケーションのログは `logs/task_manager.log` に記録されます：

```bash
# ログファイルの確認
tail -f logs/task_manager.log

# エラーログのみを表示
grep ERROR logs/task_manager.log
```

### デバッグモードでの実行

詳細なデバッグ情報を取得するには：

```python
# main.py の先頭に追加
import logging
logging.basicConfig(level=logging.DEBUG)
```

### パフォーマンス問題

#### 大量のタスクでの動作が重い場合

**解決方法**:
- データベースインデックスの確認
- タスクフィルタリングの活用
- 不要なタスクの削除またはアーカイブ

#### メモリ使用量が多い場合

**解決方法**:
- アプリケーションの再起動
- 不要なプロセスの終了
- システムリソースの確認

### サポート情報

問題が解決しない場合は、以下の情報を含めて報告してください：

1. **環境情報**:
   - OS バージョン
   - Python バージョン
   - 依存関係のバージョン

2. **エラー情報**:
   - エラーメッセージの全文
   - スタックトレース
   - ログファイルの関連部分

3. **再現手順**:
   - 問題が発生する具体的な操作手順
   - 期待される動作と実際の動作

---

## ライセンス

このプロジェクトは [MIT License](LICENSE) の下で公開されています。

## 貢献

プロジェクトへの貢献を歓迎します。以下の手順に従ってください：

1. このリポジトリをフォーク
2. 機能ブランチを作成 (`git checkout -b feature/amazing-feature`)
3. 変更をコミット (`git commit -m 'feat: Add amazing feature'`)
4. ブランチにプッシュ (`git push origin feature/amazing-feature`)
5. プルリクエストを作成

## 更新履歴

### v1.0.0 (2025-XX-XX)
- 初回リリース
- 基本的なタスク管理機能
- Windows Toast通知機能
- Backlog連携機能
- 日報生成機能
- テンプレート編集機能

---

このドキュメントは継続的に更新されます。最新の情報については、プロジェクトのリポジトリを確認してください。
```
