from sqlalchemy.orm import joinedload, Session
from sqlalchemy import or_, and_, func as sql_func, cast, Date as SQLDate
from app.database.db import SessionLocal
from app.models.task import Task
from app.models.comment import Comment
from datetime import datetime, date, timedelta
from typing import List, Optional

class TaskService:
    @staticmethod
    def create_task(
        title: str,
        description: Optional[str] = None,
        due_date: Optional[datetime] = None,
        status: str = '未着手',
        project: Optional[str] = None,
        completed: bool = False,
        repeat: Optional[str] = None,
        start_date: Optional[datetime] = None,
        estimated_time: Optional[float] = None,
        actual_time: Optional[float] = 0.0,
        actual_start_date: Optional[datetime] = None,
        actual_end_date: Optional[datetime] = None,
        source: Optional[str] = None,
        issue_key: Optional[str] = None,
        backlog_project_id: Optional[str] = None,
        source_url: Optional[str] = None,
        reminder_enabled: bool = False,
        reminder_days: Optional[int] = None
    ):
        db = SessionLocal()
        try:
            new_task = Task(
                title=title,
                description=description,
                due_date=due_date,
                status=status,
                project=project if project != "なし" else None,
                completed=completed,
                repeat=repeat,
                start_date=start_date,
                estimated_time=estimated_time,
                actual_time=actual_time,
                actual_start_date=actual_start_date,
                actual_end_date=actual_end_date,
                source=source,
                issue_key=issue_key,
                backlog_project_id=backlog_project_id,
                source_url=source_url,
                reminder_enabled=reminder_enabled,
                reminder_days=reminder_days
            )
            db.add(new_task)
            db.commit()
            db.refresh(new_task)
            return new_task
        finally:
            db.close()

    @staticmethod
    def get_task_by_id(task_id: int):
        db = SessionLocal()
        try:
            task = db.query(Task).options(joinedload(Task.comments)).filter(Task.id == task_id).first()
            return task
        finally:
            db.close()

    @staticmethod
    def get_tasks_by_filter(
        current_filter: str, # 例えば "すべてのタスク", "今日のタスク", "未完了タスク", "完了したタスク", "プロジェクト"
        project_name_filter: Optional[str] = None # current_filter が "プロジェクト" の場合に使用
    ) -> List[Task]:
        """
        指定されたフィルター条件に基づいてタスクリストを取得し、元のロジックに従って並べ替えます。
        """
        db = SessionLocal()
        try:
            query = db.query(Task).options(joinedload(Task.comments))
            today = date.today()
            # "今日のタスク"フィルター条件の処理
            if current_filter == "今日のタスク":
                # 条件1: 開始日が今日以前で未完了のタスク
                # 条件2: 期限日が今日以前で未完了のタスク（遅延含む）
                # 条件3: 今日完了したタスク
                
                # CustomDateTimeが日付を'YYYY/MM/DD'形式の文字列として保存するため、
                # DBのCAST機能に依存せず、文字列として比較します。
                # この形式の文字列比較は、日付の前後関係を正しく判定できます。
                today_str = today.strftime('%Y/%m/%d')
                
                today_filter = or_(
                    # 条件1 & 2: 開始日または期限日が今日以前で、かつ未完了
                    and_(
                        or_(
                            Task.start_date <= today_str,
                            Task.due_date <= today_str
                        ),
                        Task.status != '完了'
                    ),
                    # 条件3: 今日完了した
                    and_(
                        Task.actual_end_date == today_str,
                        Task.status == '完了'
                    )
                )
                query = query.filter(today_filter)
                tasks = query.all()
            elif current_filter == "未完了タスク":
                # Use status field instead of completed field for more accurate filtering
                query = query.filter(Task.status != '完了')
                tasks = query.all()
                
            elif current_filter == "完了したタスク":
                query = query.filter(Task.status == '完了')
                tasks = query.all()
                
            elif current_filter == "すべてのタスク":
                tasks = query.all()
                
            elif current_filter == "プロジェクト" and project_name_filter is not None:
                if project_name_filter == "なし":
                    query = query.filter(Task.project == None)
                else:
                    # プロジェクト名の大文字小文字を区別せず、前後のスペースを無視してフィルタリング
                    query = query.filter(sql_func.lower(sql_func.trim(Task.project)) == project_name_filter.strip().lower())
                tasks = query.all()
                
            else:
                tasks = query.all()            # Sorting logic from original task_manager.py
            # 1. completion_order (completed tasks last)
            # 2. due_date_order (tasks without due_date last, then by due_date)
            # 3. title_order            
            def sort_key(task_item: Task):
                completion_order = 1 if task_item.status == '完了' else 0
                due_date_actual = None
                if task_item.due_date:
                    if isinstance(task_item.due_date, datetime):
                        due_date_actual = task_item.due_date
                    elif isinstance(task_item.due_date, date): # Should not happen with SQLAlchemy DateTime
                        due_date_actual = datetime.combine(task_item.due_date, datetime.min.time())
                    elif isinstance(task_item.due_date, str):
                        # Use the safer date parsing method
                        try:
                            parsed_date = TaskService._parse_date_string(task_item.due_date)
                            if parsed_date:
                                due_date_actual = parsed_date
                        except Exception as e:
                            print(f"Error parsing date in sort_key: {e}, date: {task_item.due_date}")
                
                due_date_order = due_date_actual if due_date_actual else datetime.max
                title_order = task_item.title if task_item.title else ""
                return (completion_order, due_date_order, title_order)
            
            return sorted(tasks, key=sort_key)
        finally:
            db.close()

    @staticmethod
    def update_task(task_id: int, **kwargs):
        db = SessionLocal()
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return None
            
            for key, value in kwargs.items():
                if hasattr(task, key):
                    setattr(task, key, value)
            
            db.commit()
            db.refresh(task)
            return task
        except Exception as e:
            db.rollback()
            print(f"Error updating task {task_id}: {e}")
            return None
        finally:
            db.close()

    @staticmethod
    def delete_task(task_id: int) -> bool:
        db = SessionLocal()
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return False
            
            db.delete(task) 
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error deleting task {task_id}: {e}")
            return False
        finally:
            db.close()

    @staticmethod
    def get_all_project_names() -> List[str]:
        db = SessionLocal()
        try:
            projects = db.query(Task.project).distinct().filter(Task.project != None).all()
            
            project_names = [p[0] for p in projects if p[0]]
            project_names.sort() 
            
            project_names.insert(0, "なし")
            
            return project_names
        finally:
            db.close()

    @staticmethod
    def add_comment_to_task(task_id: int, content: str):
        db = SessionLocal()
        try:
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                return None
            
            new_comment = Comment(
                text=content,
                task_id=task_id,
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )
            
            db.add(new_comment)
            db.commit()
            db.refresh(new_comment)
            return new_comment
        except Exception as e:
            db.rollback()
            print(f"Error adding comment to task {task_id}: {e}")
            return None
        finally:
            db.close()

    @staticmethod
    def get_comment_by_id(comment_id: int):
        db = SessionLocal()
        try:
            comment = db.query(Comment).filter(Comment.id == comment_id).first()
            return comment
        finally:
            db.close()

    @staticmethod
    def update_comment(comment_id: int, content: str):
        db = SessionLocal()
        try:
            comment = db.query(Comment).filter(Comment.id == comment_id).first()
            if not comment:
                return None
            
            comment.text = content
            
            db.commit()
            db.refresh(comment)
            return comment
        except Exception as e:
            db.rollback()
            print(f"Error updating comment {comment_id}: {e}")
            return None
        finally:
            db.close()

    @staticmethod
    def delete_comment(comment_id: int) -> bool:
        db = SessionLocal()
        try:
            comment = db.query(Comment).filter(Comment.id == comment_id).first()
            if not comment:
                return False

            db.delete(comment)
            db.commit()
            return True
        except Exception as e:
            db.rollback()
            print(f"Error deleting comment {comment_id}: {e}")
            return False
        finally:
            db.close()

    @staticmethod
    def find_task_by_backlog_key(issue_key: str):
        """Backlogのissue_keyでタスクを検索する"""
        if not issue_key:
            return None

        db = SessionLocal()
        try:
            task = db.query(Task).filter(Task.issue_key == issue_key).first()
            return task
        finally:
            db.close()

    @staticmethod
    def add_task(**kwargs):
        """create_taskのエイリアス（Backlog連携用）"""
        return TaskService.create_task(**kwargs)

    @staticmethod
    def _parse_date_string(date_string):
        """Helper method to parse date strings in various formats to datetime objects."""
        if not date_string:
            return None
            
        if isinstance(date_string, (datetime, date)):
            # If it's already a datetime or date, just return it
            if isinstance(date_string, date) and not isinstance(date_string, datetime):
                # Convert plain date to datetime for consistency
                return datetime.combine(date_string, datetime.min.time())
            return date_string
            
        if isinstance(date_string, str):
            # Handle common date formats
            try:
                # Try YYYY/MM/DD format first (from the database)
                return datetime.strptime(date_string, '%Y/%m/%d')
            except ValueError:
                try:
                    # Also try ISO format YYYY-MM-DD
                    return datetime.strptime(date_string, '%Y-%m-%d')
                except ValueError:
                    # If both formats fail, try to handle malformed date strings by reformatting
                    if '/' in date_string:
                        try:
                            parts = date_string.split('/')
                            if len(parts) == 3:
                                year, month, day = parts
                                # Ensure we have proper numeric values
                                year_int = int(year)
                                month_int = int(month)
                                day_int = int(day)
                                # Validate ranges
                                if 1 <= month_int <= 12 and 1 <= day_int <= 31:
                                    # Create proper datetime object
                                    return datetime(year_int, month_int, day_int)
                        except (ValueError, IndexError) as e:
                            print(f"Warning: Could not parse date string with slashes: {date_string}, error: {str(e)}")
                    
                    print(f"Warning: Could not parse date string: {date_string}")
                    return None
        
        return None
