from PyQt5.QtCore import QDate
from datetime import datetime, date
from app.models.task import Task as TaskModel

class FormatController:
    def __init__(self):
        pass

    def parse_date(self, date_str_or_qdate):
        if not date_str_or_qdate:
            return None
        if isinstance(date_str_or_qdate, QDate):
            if date_str_or_qdate.isValid():
                return datetime(date_str_or_qdate.year(), date_str_or_qdate.month(), date_str_or_qdate.day())
            return None
        if isinstance(date_str_or_qdate, str):
            try:
                return datetime.strptime(date_str_or_qdate, '%Y/%m/%d')
            except ValueError:
                try:
                    return datetime.strptime(date_str_or_qdate, '%Y-%m-%d')
                except ValueError:
                    return None
        if isinstance(date_str_or_qdate, (datetime, date)):
            return date_str_or_qdate
        return None

    def format_qdate_to_datetime(self, q_date: QDate) -> datetime | None:
        if q_date and q_date.isValid():
            return datetime(q_date.year(), q_date.month(), q_date.day())
        return None

    def format_task_for_view(self, task_orm_object: TaskModel):
        """Converts a Task ORM object to a dictionary suitable for the view's task list."""
        if not task_orm_object:
            return None
        repeat_text = task_orm_object.repeat_display_text()
        
        # Handle due_date formatting safely
        due_date_str = None
        if task_orm_object.due_date:
            if hasattr(task_orm_object.due_date, 'strftime'):
                due_date_str = task_orm_object.due_date.strftime('%Y/%m/%d')
            else:
                due_date_str = str(task_orm_object.due_date)

        return {
            'id': task_orm_object.id,
            'title': task_orm_object.title,
            'due_date': due_date_str,
            'status': task_orm_object.status,
            'completed': task_orm_object.status == '完了',
            'repeat': repeat_text,
        }

    def format_task_details_for_view(self, task_orm: TaskModel):
        """Converts a Task ORM object to a detailed dictionary for the view."""
        if not task_orm:
            return {}
        
        comments_list = []
        if task_orm.comments:
            sorted_comments = sorted(task_orm.comments, key=lambda c: c.task_id)
            for comment_orm in sorted_comments:
                comments_list.append({
                    'id': comment_orm.id,
                    'content': comment_orm.text,
                    'task_id': comment_orm.task_id
                })
        
        planned_period_text = ""

        # Handle start_date formatting safely
        start_date_str = None
        if task_orm.start_date:
            if hasattr(task_orm.start_date, 'strftime'):
                start_date_str = task_orm.start_date.strftime('%Y/%m/%d')
            else:
                start_date_str = str(task_orm.start_date)

        # Handle due_date formatting safely
        due_date_str = None
        if task_orm.due_date:
            if hasattr(task_orm.due_date, 'strftime'):
                due_date_str = task_orm.due_date.strftime('%Y/%m/%d')
            else:
                due_date_str = str(task_orm.due_date)

        if start_date_str and due_date_str:
            planned_period_text = f"予定期間: {start_date_str} - {due_date_str}"
        elif due_date_str:
            planned_period_text = f"期限日: {due_date_str}"
        elif start_date_str:
            planned_period_text = f"開始日: {start_date_str}"
        else:
            planned_period_text = "予定期間: 未設定"
        
        return {
            'id': task_orm.id,
            'title': task_orm.title,
            'description': task_orm.description if task_orm.description is not None else "",
            'status': task_orm.status,
            'project': task_orm.project if task_orm.project else "なし",
            'comments': comments_list,
            'completed': task_orm.status == '完了',
            
            'planned_period': planned_period_text,
            'due_date': due_date_str if due_date_str else '',
            'due_date_raw': task_orm.due_date,
            'start_date_raw': task_orm.start_date,

            'repeat': task_orm.repeat if task_orm.repeat else 'なし',
            'repeat_display': task_orm.repeat_display_text(),

            'estimated_time': task_orm.estimated_time if task_orm.estimated_time is not None else "未設定",
            'estimated_time_raw': task_orm.estimated_time,

            'actual_time': task_orm.actual_time if task_orm.actual_time is not None else 0.0,
            'actual_start_date_raw': task_orm.actual_start_date,
            'actual_end_date_raw': task_orm.actual_end_date,

            'source': task_orm.source,
            'issue_key': task_orm.issue_key,
            'source_url': task_orm.source_url,
            'backlog_project_id': task_orm.backlog_project_id,            
            'reminder_enabled': task_orm.reminder_enabled,
            'reminder_days': task_orm.reminder_days
        }
